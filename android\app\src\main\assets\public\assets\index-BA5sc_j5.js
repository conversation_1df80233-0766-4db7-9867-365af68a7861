import{r as e,a as t,R as r}from"./vendor-VIHZQHJq.js";import{P as s,L as o,C as n,S as a,I as l,a as i,A as c,M as d,b as u}from"./icons-CA9rD440.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const r of e)if("childList"===r.type)for(const e of r.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();var x={exports:{}},m={},g=e,f=Symbol.for("react.element"),h=Symbol.for("react.fragment"),p=Object.prototype.hasOwnProperty,b=g.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,y={key:!0,ref:!0,__self:!0,__source:!0};function w(e,t,r){var s,o={},n=null,a=null;for(s in void 0!==r&&(n=""+r),void 0!==t.key&&(n=""+t.key),void 0!==t.ref&&(a=t.ref),t)p.call(t,s)&&!y.hasOwnProperty(s)&&(o[s]=t[s]);if(e&&e.defaultProps)for(s in t=e.defaultProps)void 0===o[s]&&(o[s]=t[s]);return{$$typeof:f,type:e,key:n,ref:a,props:o,_owner:b.current}}m.Fragment=h,m.jsx=w,m.jsxs=w,x.exports=m;var j=x.exports,v={},N=t;v.createRoot=N.createRoot,v.hydrateRoot=N.hydrateRoot;const k="01159296333",C=()=>{const[t,r]=e.useState("corrector"),[s,o]=e.useState({originalText:"",processedText:"",selectedLanguage:"en",resultType:""});return j.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-blue-600 to-purple-700 flex items-center justify-center p-4 sm:p-6 font-sans antialiased text-gray-900",children:["corrector"===t&&j.jsx(T,{setCurrentPage:r,setProcessingResults:o}),"results"===t&&j.jsx(P,{processingResults:s,setCurrentPage:r}),"about"===t&&j.jsx(S,{setCurrentPage:r})]})},T=({setCurrentPage:t,setProcessingResults:r})=>{const[i,c]=e.useState(""),[d,u]=e.useState("en"),[x,m]=e.useState(!1),[g,f]=e.useState(!1),[h,p]=e.useState(!1),[b,y]=e.useState(""),w=async e=>{let t=[];t.push({role:"user",parts:[{text:e}]});const r={contents:t},s=await fetch("https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=AIzaSyAeHER83kNZrdtR1kulVkIpN7LQoEOVbtk",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)});if(!s.ok)throw new Error(`HTTP error! status: ${s.status}`);const o=await s.json();if(o.candidates&&o.candidates.length>0&&o.candidates[0].content&&o.candidates[0].content.parts&&o.candidates[0].content.parts.length>0)return o.candidates[0].content.parts[0].text;throw new Error("Failed to get a valid response from the AI.")};return j.jsxs("div",{className:"bg-white rounded-3xl shadow-2xl p-6 sm:p-8 w-full max-w-lg mx-auto flex flex-col items-center animate-fade-in transform transition-all duration-300 ease-in-out hover:scale-[1.01] border-b-8 border-blue-500 relative",children:[j.jsxs("h1",{className:"text-3xl sm:text-4xl font-extrabold text-center text-gray-900 mb-6 flex items-center justify-center gap-3",children:[j.jsx(s,{className:"h-9 w-9 sm:h-10 sm:w-10 text-purple-600",strokeWidth:2.5}),j.jsx("span",{className:"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-700",children:"المدقق النحوي الذكي"})]}),j.jsxs("div",{className:"flex justify-center mb-6 space-x-4 w-full",children:[j.jsx("button",{onClick:()=>u("en"),className:"flex-1 px-4 py-2 sm:px-6 sm:py-3 rounded-full text-base sm:text-lg font-semibold transition duration-300 ease-in-out focus:outline-none focus:ring-4 focus:ring-blue-300 transform hover:-translate-y-0.5 shadow-md "+("en"===d?"bg-blue-600 text-white shadow-lg":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:"الإنجليزية"}),j.jsx("button",{onClick:()=>u("ar"),className:"flex-1 px-4 py-2 sm:px-6 sm:py-3 rounded-full text-base sm:text-lg font-semibold transition duration-300 ease-in-out focus:ring-4 focus:ring-purple-300 transform hover:-translate-y-0.5 shadow-md "+("ar"===d?"bg-purple-600 text-white shadow-lg":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:"العربية"})]}),j.jsx("textarea",{className:"w-full p-4 mb-6 border-2 border-gray-300 rounded-xl resize-y focus:outline-none focus:border-blue-500 text-lg text-gray-800 placeholder-gray-400 min-h-[140px] max-h-[300px] shadow-inner font-inter leading-relaxed custom-scrollbar",placeholder:`اكتب نصك هنا لتصحيحه (${"en"===d?"الإنجليزية":"العربية"})...`,value:i,onChange:e=>c(e.target.value),rows:"7",dir:"ar"===d?"rtl":"ltr"}),j.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 w-full mb-4",children:[j.jsx("button",{onClick:async()=>{if(""===i.trim())return y("يرجى إدخال نص للتصحيح"),void p(!0);p(!1),m(!0);const e=`${`Correct the grammar, spelling, and syntax of the following ${"en"===d?"English":"Arabic"} text. Focus on common grammatical errors, punctuation, verb tenses, subject-verb agreement, and accurate spelling. Provide only the corrected text, no additional explanations or introductions.`}\n\n${i}`;try{const s=await w(e);r({originalText:i,processedText:s,selectedLanguage:d,resultType:"correction"}),t("results")}catch(s){console.error("Error correcting grammar:",s),y(s.message||"حدث خطأ أثناء التصحيح. يرجى المحاولة مرة أخرى."),p(!0)}finally{m(!1)}},disabled:x||g||""===i.trim(),className:"flex-1 bg-gradient-to-r from-blue-600 to-purple-700 text-white font-bold py-3 rounded-xl text-lg sm:text-xl uppercase tracking-wider shadow-lg hover:shadow-xl transition-all duration-300 ease-in-out transform hover:-translate-y-1 focus:outline-none focus:ring-4 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2",children:x?j.jsxs(j.Fragment,{children:[j.jsx(o,{className:"animate-spin h-6 w-6 text-white"}),"جاري التصحيح..."]}):j.jsxs(j.Fragment,{children:[j.jsx(n,{className:"h-6 w-6",strokeWidth:2}),"تصحيح النص"]})}),j.jsx("button",{onClick:async()=>{if(""===i.trim())return y("يرجى إدخال نص لإعادة الصياغة"),void p(!0);p(!1),f(!0);const e=`${`Rephrase the following ${"en"===d?"English":"Arabic"} text to improve its clarity, tone, and flow, without changing its core meaning. Provide only the rephrased text.`}\n\n${i}`;try{const s=await w(e);r({originalText:i,processedText:s,selectedLanguage:d,resultType:"rephrase"}),t("results")}catch(s){console.error("Error rephrasing text:",s),y(s.message||"حدث خطأ أثناء إعادة الصياغة. يرجى المحاولة مرة أخرى."),p(!0)}finally{f(!1)}},disabled:x||g||""===i.trim(),className:"flex-1 bg-gradient-to-r from-purple-600 to-pink-700 text-white font-bold py-3 rounded-xl text-lg sm:text-xl uppercase tracking-wider shadow-lg hover:shadow-xl transition-all duration-300 ease-in-out transform hover:-translate-y-1 focus:outline-none focus:ring-4 focus:ring-pink-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2",children:g?j.jsxs(j.Fragment,{children:[j.jsx(o,{className:"animate-spin h-6 w-6 text-white"}),"جاري إعادة الصياغة..."]}):j.jsxs(j.Fragment,{children:[j.jsx(a,{className:"h-6 w-6",strokeWidth:2}),"✨ إعادة صياغة النص"]})})]}),h&&j.jsx("div",{className:"mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-lg text-center text-sm shadow-md animate-fade-in w-full",children:b||"حدث خطأ. يرجى التأكد من اتصالك بالإنترنت، أو أن النص المدخل ليس فارغًا."}),j.jsxs("button",{onClick:()=>t("about"),className:"mt-6 w-full bg-gray-200 text-gray-700 font-bold py-2 rounded-xl text-md shadow-sm hover:bg-gray-300 transition-colors duration-200 focus:outline-none focus:ring-4 focus:ring-gray-400 flex items-center justify-center gap-2",children:[j.jsx(l,{className:"h-5 w-5",strokeWidth:2}),"حول المطور"]})]})},P=({processingResults:t,setCurrentPage:r})=>{const{originalText:s,processedText:o,selectedLanguage:l,resultType:d}=t,[u,x]=e.useState("نسخ النص");e.useEffect((()=>{"correction"===d?x("نسخ النص المصحح"):"rephrase"===d&&x("نسخ النص المعاد صياغته")}),[d]);const m="correction"===d?"النص المصحح:":"النص المعاد صياغته:",g="correction"===d?"نتائج التصحيح":"نتائج إعادة الصياغة",f="correction"===d?j.jsx(n,{className:"h-9 w-9 sm:h-10 sm:w-10 text-green-600",strokeWidth:2.5}):j.jsx(a,{className:"h-9 w-9 sm:h-10 sm:w-10 text-pink-600",strokeWidth:2.5}),h="correction"===d?"border-green-500":"border-pink-500";return j.jsxs("div",{className:`bg-white rounded-3xl shadow-2xl p-6 sm:p-8 w-full max-w-2xl mx-auto flex flex-col items-center animate-fade-in transform transition-all duration-300 ease-in-out hover:scale-[1.01] border-b-8 ${h}`,children:[j.jsxs("h1",{className:"text-3xl sm:text-4xl font-extrabold text-center text-gray-900 mb-6 flex items-center justify-center gap-3",children:[f,j.jsx("span",{className:"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-green-700",children:g})]}),j.jsxs("div",{className:"flex flex-col sm:flex-row gap-6 w-full mb-8",children:[j.jsxs("div",{className:"flex-1 bg-gray-50 p-4 border-2 border-gray-200 rounded-xl shadow-inner flex flex-col",children:[j.jsx("h2",{className:"text-xl font-bold text-gray-800 mb-3 text-center",children:"النص الأصلي:"}),j.jsx("p",{className:"text-gray-700 text-lg leading-relaxed whitespace-pre-wrap max-h-[250px] overflow-y-auto custom-scrollbar flex-grow font-inter",dir:"ar"===l?"rtl":"ltr",children:s})]}),j.jsxs("div",{className:`flex-1 ${"correction"===d?"bg-green-50 border-green-200":"bg-pink-50 border-pink-200"} p-4 border-2 rounded-xl shadow-inner flex flex-col`,children:[j.jsx("h2",{className:"text-xl font-bold text-gray-800 mb-3 text-center",children:m}),j.jsx("p",{className:"text-gray-700 text-lg leading-relaxed whitespace-pre-wrap max-h-[250px] overflow-y-auto custom-scrollbar flex-grow font-inter",dir:"ar"===l?"rtl":"ltr",children:o}),j.jsxs("button",{onClick:()=>{if(!o)return;const e=document.createElement("textarea");e.value=o,document.body.appendChild(e),e.select();try{document.execCommand("copy"),x("تم النسخ!"),setTimeout((()=>{"correction"===d?x("نسخ النص المصحح"):"rephrase"===d&&x("نسخ النص المعاد صياغته")}),2e3)}catch(t){console.error("Failed to copy text:",t);const e=document.createElement("div");e.textContent="فشل النسخ. يرجى النسخ يدويًا.",e.className="fixed bottom-4 left-1/2 -translate-x-1/2 bg-red-600 text-white p-3 rounded-lg shadow-lg z-50 animate-fade-in-out",document.body.appendChild(e),setTimeout((()=>{e.remove()}),3e3)}finally{document.body.removeChild(e)}},className:`mt-4 w-full ${"correction"===d?"bg-green-600 hover:bg-green-700 focus:ring-green-300":"bg-pink-600 hover:bg-pink-700 focus:ring-pink-300"} text-white font-bold py-2 rounded-xl text-md shadow-md transition-colors duration-200 focus:outline-none focus:ring-4 flex items-center justify-center gap-2`,children:[j.jsx(i,{className:"h-5 w-5",strokeWidth:2}),u]})]})]}),j.jsxs("button",{onClick:()=>r("corrector"),className:"w-full bg-gradient-to-r from-purple-600 to-blue-700 text-white font-bold py-3 rounded-xl text-lg shadow-lg hover:shadow-xl transition-all duration-300 ease-in-out transform hover:-translate-y-1 focus:outline-none focus:ring-4 focus:ring-blue-500 flex items-center justify-center gap-2",children:[j.jsx(c,{className:"h-5 w-5",strokeWidth:2}),"العودة إلى المدقق"]})]})},S=({setCurrentPage:e})=>j.jsxs("div",{className:"bg-white rounded-3xl shadow-2xl p-6 sm:p-8 w-full max-w-md mx-auto flex flex-col items-center text-center animate-fade-in transform transition-all duration-300 ease-in-out border-b-8 border-purple-500",children:[j.jsxs("h2",{className:"text-3xl sm:text-4xl font-extrabold text-gray-900 mb-6 flex items-center justify-center gap-3",children:[j.jsx(l,{className:"h-9 w-9 sm:h-10 sm:w-10 text-blue-600",strokeWidth:2.5}),j.jsx("span",{className:"text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-blue-700",children:"معلومات المطور"})]}),j.jsx("p",{className:"text-xl sm:text-2xl font-bold text-gray-800 mb-6",children:"م / كريم وهيب"}),j.jsxs("div",{className:"flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-6 w-full mb-8",children:[j.jsxs("a",{href:`https://wa.me/${k}`,target:"_blank",rel:"noopener noreferrer",className:"flex items-center justify-center gap-3 text-green-600 hover:text-green-700 transition-colors duration-200 text-lg font-medium p-3 rounded-full bg-green-50 hover:bg-green-100 shadow-md transform hover:scale-105",children:[j.jsx(d,{className:"h-7 w-7 fill-current"}),"واتساب"]}),j.jsxs("a",{href:`tel:+${k}`,className:"flex items-center justify-center gap-3 text-blue-600 hover:text-blue-700 transition-colors duration-200 text-lg font-medium p-3 rounded-full bg-blue-50 hover:bg-blue-100 shadow-md transform hover:scale-105",children:[j.jsx(u,{className:"h-7 w-7 fill-current"}),k]})]}),j.jsxs("button",{onClick:()=>e("corrector"),className:"mt-4 w-full bg-gradient-to-r from-purple-600 to-blue-700 text-white font-bold py-3 rounded-xl text-lg shadow-lg hover:shadow-xl transition-all duration-300 ease-in-out transform hover:-translate-y-1 focus:outline-none focus:ring-4 focus:ring-blue-500 flex items-center justify-center gap-2",children:[j.jsx(c,{className:"h-5 w-5",strokeWidth:2}),"العودة إلى المدقق"]})]});v.createRoot(document.getElementById("root")).render(j.jsx(r.StrictMode,{children:j.jsx(C,{})}));
