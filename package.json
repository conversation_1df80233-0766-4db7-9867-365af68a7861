{"name": "smart-grammar-checker", "private": true, "version": "1.0.0", "type": "module", "description": "مدقق نحوي ذكي باللغتين العربية والإنجليزية", "author": "كريم وهيب", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "android:build": "npm run build && npx cap copy android && npx cap sync android", "android:open": "npx cap open android", "android:run": "npm run android:build && npm run android:open"}, "dependencies": {"@capacitor/android": "^7.3.0", "@capacitor/cli": "^7.3.0", "@capacitor/core": "^7.3.0", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "terser": "^5.41.0", "vite": "^5.0.8"}, "engines": {"node": ">=18.0.0"}}