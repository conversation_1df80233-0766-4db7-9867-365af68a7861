# المدقق النحوي الذكي

مدقق نحوي ذكي باللغتين العربية والإنجليزية يستخدم الذكاء الاصطناعي لتصحيح النصوص وإعادة صياغتها.

## المميزات

- ✅ تصحيح نحوي ذكي للنصوص العربية والإنجليزية
- ✨ إعادة صياغة النصوص لتحسين الوضوح والأسلوب
- 🌐 واجهة مستخدم متجاوبة تدعم اللغتين
- 🎨 تصميم عصري وجذاب
- 📱 متوافق مع جميع الأجهزة

## التقنيات المستخدمة

- **React 18** - مكتبة واجهة المستخدم
- **Vite** - أداة البناء والتطوير
- **Tailwind CSS** - إطار عمل CSS
- **Lucide React** - مكتبة الأيقونات
- **Google Gemini AI** - الذكاء الاصطناعي للتصحيح

## التثبيت والتشغيل

### المتطلبات
- Node.js 18 أو أحدث
- npm أو yarn

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone <repository-url>
cd smart-grammar-checker
```

2. **تثبيت التبعيات**
```bash
npm install
```

3. **إعداد متغيرات البيئة**
```bash
cp .env.example .env
```

4. **إضافة مفتاح Gemini API**
   - احصل على مفتاح API من [Google AI Studio](https://makersuite.google.com/app/apikey)
   - أضف المفتاح في ملف `.env`:
```
VITE_GEMINI_API_KEY=your_api_key_here
```

5. **تشغيل المشروع محلياً**
```bash
npm run dev
```

6. **بناء المشروع للإنتاج**
```bash
npm run build
```

## النشر على Netlify

### الطريقة الأولى: النشر المباشر

1. **بناء المشروع**
```bash
npm run build
```

2. **رفع مجلد `dist` إلى Netlify**
   - اذهب إلى [Netlify](https://netlify.com)
   - اسحب مجلد `dist` إلى منطقة النشر

### الطريقة الثانية: الربط مع Git

1. **رفع المشروع إلى GitHub**
```bash
git init
git add .
git commit -m "Initial commit"
git remote add origin <your-repo-url>
git push -u origin main
```

2. **ربط المشروع مع Netlify**
   - اذهب إلى [Netlify](https://netlify.com)
   - اختر "New site from Git"
   - اختر مستودع GitHub الخاص بك
   - إعدادات البناء:
     - **Build command**: `npm run build`
     - **Publish directory**: `dist`

3. **إضافة متغيرات البيئة في Netlify**
   - اذهب إلى Site settings > Environment variables
   - أضف `VITE_GEMINI_API_KEY` مع قيمة مفتاح API

### الطريقة الثالثة: استخدام Netlify CLI

1. **تثبيت Netlify CLI**
```bash
npm install -g netlify-cli
```

2. **تسجيل الدخول**
```bash
netlify login
```

3. **النشر**
```bash
netlify deploy --prod --dir=dist
```

## إعدادات Netlify

الملف `netlify.toml` يحتوي على إعدادات مُحسنة للنشر:
- إعادة توجيه جميع المسارات إلى `index.html` (SPA)
- ضغط الملفات
- إعدادات الأمان
- تحسين التخزين المؤقت

## المطور

**م / كريم وهيب**
- 📱 واتساب: [01159296333](https://wa.me/01159296333)
- ☎️ هاتف: 01159296333

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.

## ملاحظات مهمة

- تأكد من إضافة مفتاح Gemini API الصحيح
- المشروع يتطلب اتصال بالإنترنت للعمل
- يُنصح بإعداد نطاق مخصص في Netlify لتحسين الأداء

---

## 🎉 حالة المشروع: جاهز للنشر والتطوير!

### ✅ **للنشر على الويب (Netlify):**
- مجلد `dist` جاهز للنشر
- مفتاح Gemini API مُعد: `AIzaSyAeHER83kNZrdtR1kulVkIpN7LQoEOVbtk`
- جميع التبعيات مثبتة
- الملفات محسنة ومضغوطة

### 📱 **لبناء تطبيق Android APK:**
- Capacitor مُثبت ومُكون
- مجلد `android` جاهز لـ Android Studio
- جميع الأذونات والإعدادات مُضافة
- دعم RTL للعربية مُفعل
- Network Security مُعد للـ API calls

### خطوات النشر السريع على الويب:
1. اذهب إلى [netlify.com](https://netlify.com)
2. اسحب مجلد `dist` إلى منطقة النشر
3. أضف متغير البيئة: `VITE_GEMINI_API_KEY`
4. استمتع بموقعك المنشور! 🚀

### خطوات بناء APK:
1. افتح Android Studio
2. اختر "Open Project" واختر مجلد `android`
3. انتظر Gradle Sync
4. Build > Build APK(s)
5. استمتع بتطبيقك على Android! 📱

**📖 للتفاصيل الكاملة، راجع ملف `ANDROID_BUILD_GUIDE.md`**
